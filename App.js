// import 'react-native-gesture-handler';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Provider } from 'react-redux';

import SafeAreaView from 'src/components/Safe-area-view.component';
import store from 'src/redux/store';
import GameScreen from 'src/screens/Game.screen';
import ScoreBoardScreen from 'src/screens/Score-board.screen';
import StartScreen from 'src/screens/Start.screen';
import WelcomeScreen from 'src/screens/Welcome.screen';

const Stack = createNativeStackNavigator();

const App = () => {

  return (
    <Provider store={store}>
      <NavigationContainer>
        <Stack.Navigator initialRouteName='score-board' screenOptions={{
          headerShown: false
        }}>
          <Stack.Screen name="welcome-screen" component={WelcomeScreen} options={{ title: 'Welcome' }} />
          <Stack.Screen name="start-screen" component={StartScreen} options={{ title: 'Start' }} />
          <Stack.Screen name="game-screen" component={GameScreen} options={{ title: 'Game' }} />
          <Stack.Screen name="score-board" component={ScoreBoardScreen} options={{ title: 'Scores' }} />
        </Stack.Navigator>
      </NavigationContainer>
    </Provider>
  );
}

export default App