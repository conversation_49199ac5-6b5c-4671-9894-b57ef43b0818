{"name": "sudoku-1", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/lato": "^0.2.2", "@expo/config-plugins": "~6.0.0", "@expo/prebuild-config": "~6.0.0", "@react-native-async-storage/async-storage": "1.17.11", "@react-native-community/blur": "^4.3.0", "@react-native-community/masked-view": "^0.1.11", "@react-navigation/drawer": "^6.6.0", "@react-navigation/native": "^6.1.3", "@react-navigation/native-stack": "^6.9.10", "@reduxjs/toolkit": "^1.9.3", "chalk": "^5.2.0", "escape-string-regexp": "^5.0.0", "expo": "^48.0.11", "expo-ads-admob": "^13.0.0", "expo-app-loading": "~2.1.1", "expo-constants": "~14.2.1", "expo-dev-client": "~2.2.1", "expo-font": "~11.1.1", "expo-linear-gradient": "~12.1.2", "expo-modules-core": "~1.2.7", "expo-status-bar": "~1.4.2", "expo-system-ui": "~2.2.1", "install": "^0.13.0", "lottie-ios": "^3.5.0", "lottie-react-native": "5.1.4", "moment": "^2.29.4", "patch-package": "^6.5.1", "react": "18.2.0", "react-native": "0.71.14", "react-native-gesture-handler": "~2.9.0", "react-native-get-random-values": "~1.9.0", "react-native-linear-gradient": "^2.6.2", "react-native-reanimated": "~2.14.4", "react-native-safe-area-context": "4.5.0", "react-native-safe-area-view": "^1.1.1", "react-native-screens": "~3.20.0", "react-native-svg": "13.4.0", "react-native-swipe-gestures": "^1.0.5", "react-redux": "^8.0.5", "redux": "^4.2.1", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@babel/core": "^7.12.9", "babel-plugin-module-resolver": "^5.0.0"}, "private": true}