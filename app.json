{"expo": {"name": "sudoku", "slug": "sudoku-1", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.ncps.sudoku", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.ncps.sudoku"}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "73447075-65d3-4335-9f62-12d43456ffe0"}}, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "react-native-google-mobile-ads": {"android_app_id": "ca-app-pub-xxxxxxxx~xxxxxxxx", "ios_app_id": "ca-app-pub-xxxxxxxx~xxxxxxxx"}}