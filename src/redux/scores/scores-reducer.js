import scoresActionType from "./scores.action.type";

const INITIAL_STATE = {
    list: [
        {
            id: 123,
            difficulty: 'difficulty',
            score: 123,
            duration: 11217,
            hints: 12,
            mistaks: 2,
            time: 1682266642057
        },
        {
            id: 123,
            difficulty: 'difficulty',
            score: 123,
            duration: 11217,
            hints: 12,
            mistaks: 2,
            time: 1682266642057
        },
        {
            id: 123,
            difficulty: 'difficulty',
            score: 123,
            duration: 11217,
            hints: 12,
            mistaks: 2,
            time: 1682266642057
        }
    ]
}

// id: game.id,
// difficulty: difficulty,
// score: 123,
// duration: currentTime.current,
// hints: MAX_HINT - totalHint,
// mistaks: mistakeCount.used,
// time: Date.now()

const scoreReducer = (state = INITIAL_STATE, { type, payload }) => {
    switch (type) {
        case scoresActionType.SET_NEW_SCORE:
            return {
                ...state,
                list: [...state.list, payload]
            }
        default:
            return state
    }
}

export default scoreReducer;