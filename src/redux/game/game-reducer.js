import gameActionType from "./game.action.type";

const INITIAL_STATE = {
    savedGame: null
}

const gameReducer = (state = INITIAL_STATE, { type, payload }) => {
    switch (type) {
        case gameActionType.SET_GAME:
            return {
                ...state,
                savedGame: payload
            }
        case gameActionType.CLEAR_GAME:
            return {
                ...state,
                savedGame: null
            }
        default:
            return state
    }
}

export default gameReducer;