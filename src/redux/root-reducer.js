import AsyncStorage from "@react-native-async-storage/async-storage";
import { combineReducers } from "redux";
import { persistReducer } from 'redux-persist';
import scoreReducer from "./scores/scores-reducer";
import gameReducer from "./game/game-reducer";

const persistConfig = {
    key: 'root',
    storage: AsyncStorage,
    whitelist: ['scores', 'game']
};

const rootReducer = combineReducers({
    scores: scoreReducer,
    game: gameReducer
})

// export default persistReducer(persistConfig, rootReducer);
export default rootReducer;