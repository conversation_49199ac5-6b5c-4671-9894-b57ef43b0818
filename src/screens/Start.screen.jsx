import React, { useEffect, useState } from 'react'
import { View, Image, Text, BackHandler, Alert, StatusBar, StyleSheet, TouchableHighlight, Linking } from 'react-native'
import { LinearGradient } from 'expo-linear-gradient'
import GestureRecognizer, { swipeDirections } from 'react-native-swipe-gestures';
import {
    useFonts,
    Lato_100Thin,
    Lato_300Light,
    Lato_400Regular
} from "@expo-google-fonts/lato"
import { retrieveData } from 'src/asyncStorage'
import SafeAreaView from 'src/components/Safe-area-view.component'
import ExitButton from 'src/components/buttons/Exit-button'
import ScoreButton from 'src/components/buttons/Score-button.component'
import LaunchButton from 'src/components/buttons/Launch-button.component'
import ContinueButton from 'src/components/buttons/Continue-button.component'
import GameEnums from 'src/enums/game-enums'
import Sudoku from 'src/lib/generator/sudoku'
import { connect } from 'react-redux';
import StartButton from 'src/components/buttons/Start-button.component';
import { Path, Svg } from 'react-native-svg';

import icon from 'assets/icon.png'

const backPressed = () => {
    Alert.alert(
        'Exit App',
        'Do you want to exit?',
        [
            { text: 'No', onPress: () => console.log('Cancel Pressed'), style: 'cancel' },
            { text: 'Yes', onPress: () => BackHandler.exitApp() },
        ],
        { cancelable: false });
    return true;
}

const OptionButton = ({ children, ...props }) => {
    const styles = StyleSheet.create({
        optionButton: {
            width: '100%',
            marginTop: 5,
            borderRadius: 20,
            overflow: 'hidden',
            shadowColor: "#000",
            shadowOffset: {
                width: 0,
                height: 1,
            },
            shadowOpacity: 0.1,
            shadowRadius: 10,

            elevation: 1,
        }
    })
    return (<TouchableHighlight {...props} underlayColor="#DDDDDD" style={styles.optionButton} >
        <LinearGradient start={[0, 0.5]}
            end={[1, 0.5]}
            colors={['#cccff7', 'transparent', 'transparent', 'transparent', 'transparent', 'transparent']}
            style={{ alignItems: 'center' }}>
            <LinearGradient start={[0, 0.5]}
                end={[1, 0.2]}
                colors={['transparent', '#fff', '#fff', '#fff']}
                style={{
                    width: '100%', alignItems: 'center',
                    paddingVertical: 15
                }}>
                <View style={{
                    width: '100%',
                    alignItems: 'center',
                }}>
                    <Text style={{ textTransform: 'uppercase' }}>{children.replace('-', ' ')}</Text>
                </View>
            </LinearGradient>
        </LinearGradient>
    </TouchableHighlight>)
}

const FullPopup = ({ navigation, setIsGameModeOpen }) => {
    const styles = StyleSheet.create({
        popupContainer: {
            position: 'absolute',
            bottom: 0,
            top: 0,
            right: 0,
            left: 0,
            width: '100%',
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            backgroundColor: '#eceff7f2',
            paddingBottom: 5,
            // justifyContent: 'flex-end'
        },
        buttonContainer: {
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: '#fff',
            paddingHorizontal: 15,
            paddingVertical: 10,
            borderTopLeftRadius: 30,
            borderTopRightRadius: 30,

            position: 'absolute',
            bottom: 0
        },
        optionButton: {
            paddingVertical: 8,
            width: '100%',
        }
    })

    const onClickNavigate = (difficulty) => {
        navigation.navigate('game-screen', {
            state: GameEnums.NEW_GAME,
            difficulty: difficulty
        })
        // setIsGameModeOpen(false)
    }
    const onSwipeDown = (gestureName, gestureState) => {
        const { SWIPE_UP, SWIPE_DOWN, SWIPE_LEFT, SWIPE_RIGHT } = swipeDirections;
        setIsGameModeOpen(false)
    }
    return (
        <View style={styles.popupContainer}>
            <View style={styles.buttonContainer}>
                <GestureRecognizer config={{
                    velocityThreshold: 0.3,
                    directionalOffsetThreshold: 80
                }} onSwipeDown={(direction, state) => onSwipeDown(direction, state)}>
                    <TouchableHighlight underlayColor="#DDDDDD" onPress={onSwipeDown} >
                        <View style={{ height: 16, width: 52, marginBottom: 4, backgroundColor: '#cccff7', borderRadius: 50 }} ></View>
                    </TouchableHighlight>
                </GestureRecognizer>
                {
                    Object.keys(Sudoku.DIFFICULTY_LAVAL).map((ele, i) =>
                        <OptionButton onPress={() => onClickNavigate(ele)} key={i} >{ele}</OptionButton>
                    )
                }
            </View>
        </View>
    )
}

const StartScreen = ({ navigation, savedGame }) => {
    const [isGameModeOpen, setIsGameModeOpen] = useState(true)
    let [isFontsLoaded] = useFonts({ Lato_100Thin, Lato_300Light, Lato_400Regular });
    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            setIsGameModeOpen(false)
        });
        return unsubscribe;
    }, [navigation])

    if (!isFontsLoaded) {
        return <></>
    }
    return (
        <SafeAreaView>
            <StatusBar barStyle="dark-content" backgroundColor={'transparent'} translucent={true} />
            <View style={{
                flex: 1,
                width: '100%',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center'
            }}>
                <View style={{
                    alignItems: 'center',
                    marginBottom: 28
                }}>
                    <Image source={icon}
                        style={{
                            width: 200,
                            height: 200
                        }} />
                </View>
                {
                    !!savedGame &&
                    <StartButton title={'Continue'} onPress={(e) => {
                        navigation.navigate('game-screen', {
                            state: GameEnums.CONTINUE
                        })
                    }}>
                        <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width='28px' height='28px'>
                            <Path strokeLinecap="round" strokeLinejoin="round" stroke="#555770" d="M21 7.5V18M15 7.5V18M3 16.811V8.69c0-.864.933-1.406 1.683-.977l7.108 4.061a1.125 1.125 0 010 1.954l-7.108 4.061A1.125 1.125 0 013 16.811z" />
                        </Svg>
                    </StartButton>
                }
                <View style={{
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    paddingHorizontal: 15,
                }}>

                    <StartButton title={'New Game'} onPress={(e) => {
                        setIsGameModeOpen(!isGameModeOpen)
                    }}>
                        <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width='28px' height='28px'>
                            <Path strokeLinecap="round" stroke="#555770" strokeLinejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.348a1.125 1.125 0 010 1.971l-11.54 6.347a1.125 1.125 0 01-1.667-.985V5.653z" />
                        </Svg>
                    </StartButton>

                    {/* <StartButton title={'Scores'} onPress={(e) => {
                        navigation.push('score-board')
                    }}>
                        <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" height='28px' width='28px' >
                            <Path strokeLinecap="round" stroke="#555770" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                        </Svg>
                    </StartButton> */}

                    <StartButton title={'How to Play'} onPress={() => {
                        Linking.openURL('https://en.wikipedia.org/wiki/Sudoku')
                    }}>
                        <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width={'28px'} height={'28px'}>
                            <Path strokeLinecap="round" stroke={'#555770'} strokeLinejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
                        </Svg>
                    </StartButton>

                    <StartButton title={'Exit'} onPress={() => {
                        backPressed()
                    }}>
                        <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width={'28px'} height={'28px'}>
                            <Path strokeLinecap="round" stroke={'#555770'} strokeLinejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                        </Svg>
                    </StartButton>
                    {/* <LaunchButton onPress={(e) => {
                        setIsGameModeOpen(!isGameModeOpen)
                    }}></LaunchButton> */}
                </View>
            </View>
            {
                isGameModeOpen &&
                <FullPopup setIsGameModeOpen={setIsGameModeOpen} navigation={navigation} />
            }
        </SafeAreaView>
    )
}

const mapStateToProps = state => ({
    savedGame: state.game.savedGame
})

export default connect(mapStateToProps)(StartScreen)