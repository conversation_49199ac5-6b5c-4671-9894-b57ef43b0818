import React, { memo, useEffect, useState } from 'react';
import { ActivityIndicator, Alert, BackHandler, StyleSheet, Text, TouchableHighlight, View } from 'react-native'
import { LinearGradient } from 'expo-linear-gradient'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { StatusBar } from 'react-native';
import { StackActions } from '@react-navigation/native'

import SudokuBoard from "src/components/Sudoku-board.component";
import SafeAreaView from "src/components/Safe-area-view.component";
import GameEnums from 'src/enums/game-enums';
import Sudoku from 'src/lib/generator/sudoku';
import { retrieveData, storeData } from 'src/asyncStorage';
import { connect } from 'react-redux';
import { setGame } from 'src/redux/game/game.action';


const GameScreen = ({ route, navigation, savedGame, setGame }) => {

    const [sudoku, setSudoku] = useState(null)
    const [isLoading, setIsLoading] = useState(true);
    const [isPlaying, setIsPlaying] = useState(true)
    const [isGameFinised, setIsGameFinised] = useState(false)

    const params = route.params

    const timerClickHandler = (value) => {
        /**
         * This function will use to update state isPlaying from PlayPause components
         */
        setIsPlaying(value)
    }

    useEffect(() => {
        let sudoku = null;

        if (params.state == GameEnums.CONTINUE && savedGame) {
            /**
             * Set the saved Game if user want to continue previous game
             */
            setSudoku(savedGame)
        } else {
            /**
             * Create a new game and set the state if user want to play new game
             */
            sudoku = new Sudoku(params.difficulty || 'medium')
            setSudoku(sudoku)
        }
    }, [])

    useEffect(() => {
        const unsubscribe = navigation.addListener('beforeRemove', (e) => {
            if (sudoku) {
                /**
                 * Save Game if
                 *  - User exit the game console
                 *  - And Game is not finished
                 */
                if (!isGameFinised) {
                    setGame(sudoku)
                }
            }
        })
        return unsubscribe
    }, [navigation, sudoku, isGameFinised])

    return <SafeAreaView>
        <StatusBar barStyle="dark-content" backgroundColor={'transparent'} translucent={true} />
        {
            !!sudoku ?
                <SudokuBoard isGameFinised={isGameFinised} setIsGameFinised={setIsGameFinised} navigation={navigation} sudoku={sudoku} isPlaying={isPlaying} timerHandler={timerClickHandler} />
                :
                <View style={{
                    flex: 1,
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <ActivityIndicator size="large" />
                </View>
        }
    </SafeAreaView>
};

const mapStateToProps = state => ({
    savedGame: state.game.savedGame
})
const mapDispatchToProps = dispatch => ({
    setGame: (data) => dispatch(setGame(data))
})

export default connect(mapStateToProps, mapDispatchToProps)(GameScreen);