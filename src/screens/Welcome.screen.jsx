import React, { useState, useEffect } from 'react'
import { StatusBar } from 'react-native'
import { View, StyleSheet, Text, Image, useWindowDimensions, ImageBackground, TouchableHighlight } from 'react-native'
import { AppLoading } from "expo-app-loading";
import {
    useFonts,
    Lato_100Thin,
    Lato_300Light,
    Lato_400Regular
} from "@expo-google-fonts/lato"

import welcomBg from 'assets/welcome-bg.jpg'
import logo from 'assets/logo.png'
import WelcomeLoading from 'src/components/Welcome-loading.component';

// Challenge your mind and have fun at the same time by playing a puzzle game - you never know what solutions you might come up with!

const WelcomeScreen = ({ navigation }) => {
    const { height, width } = useWindowDimensions()
    const [isLoading, setIsLoading] = useState(true)
    let [isFontsLoaded] = useFonts({ Lato_100Thin, Lato_300Light, Lato_400Regular });
    useEffect(() => {
        const id = setTimeout(() => {
            setIsLoading(false)
        }, 3000)
        return () => {
            clearTimeout(id)
        }
    }, [])

    if (!isFontsLoaded || isLoading) {
        return <><WelcomeLoading /></>
    }
    return (<>
        <StatusBar barStyle="light-content" backgroundColor={'transparent'} translucent={true} />
        <View style={styles().container}>
            <ImageBackground source={welcomBg} resizeMode={'cover'} style={styles({ height }).welcomImgContainer}>
                <Text style={{ fontSize: 48, fontWeight: '200', color: '#fff', fontFamily: 'Lato_300Light' }}>WELCOME</Text>

            </ImageBackground>
            <View style={styles({ width, height }).logoContainer}>
                <Text style={{ fontSize: 72, fontWeight: '100', color: '#fff', fontFamily: 'Lato_100Thin' }}>B</Text>
            </View>
            <View style={styles({ width }).bottomContainer}>
                <Text style={{
                    fontSize: 24,
                    fontWeight: '200',
                    fontFamily: 'Lato_300Light'
                }}>Challenge your mind </Text>
                <Text style={{
                    fontSize: 24,
                    fontWeight: '200',
                    fontFamily: 'Lato_300Light'
                }}>and have fun at the same time </Text>
                <TouchableHighlight style={styles({ width, height }).startButton} underlayColor="transparent" onPress={(e) => {
                    navigation.navigate('start-screen')
                }} >
                    <Text style={{
                        fontSize: 24,
                        fontWeight: '300',
                        color: '#fff',
                        fontFamily: 'Lato_400Regular'
                    }}>START</Text>
                </TouchableHighlight>
            </View>
        </View>
    </>
    )
}

const styles = (props) => StyleSheet.create({
    container: {
        flex: 1,
        position: 'relative',
        backgroundColor: '#eceff7c2',
        alignItems: 'center'
    },
    welcomImgContainer: {
        width: '100%',
        height: props?.height / 2,
        justifyContent: 'center',
        alignItems: 'center'
    },
    welcomBgImg: {
        width: '100%',
        height: '100%'
    },
    logo: {
        width: 100,
        height: 100
    },
    logoContainer: {
        // paddingHorizontal: 10,
        height: 120,
        width: 120,
        position: 'absolute',
        justifyContent: 'center',
        alignItems: 'center',
        top: '50%',
        left: '50%',
        transform: [{ translateX: -((props?.width / 2) * 100 / props?.width) }, { translateY: -((props?.height / 2) * 100 / props?.height) }],
        backgroundColor: '#2d6ddb',
        borderTopLeftRadius: 10,
        borderBottomRightRadius: 10,
        borderTopRightRadius: 30,
        borderBottomLeftRadius: 30,
    },
    bottomContainer: {
        width: props?.width - 30,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: -1,
        flex: 1
    },
    startButton: {
        marginTop: 50,
        width: 154,
        height: 54,
        backgroundColor: '#2d6ddb',
        justifyContent: 'center',
        alignItems: 'center'
    }

})

export default WelcomeScreen