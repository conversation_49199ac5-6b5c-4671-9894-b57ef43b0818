import React from 'react'
import { Text, View, ScrollView } from 'react-native'
import { StatusBar } from 'expo-status-bar'
import { Path, Svg } from 'react-native-svg'
import { connect } from 'react-redux'
import moment from 'moment'
import SafeAreaView from 'src/components/Safe-area-view.component'

const ScoreBoardScreen = ({ navigation, scores }) => {
    console.log(scores);
    return (
        <SafeAreaView>
            <StatusBar barStyle="dark-content" backgroundColor={'transparent'} translucent={true} />
            <View style={{
                flex: 1,
                width: '100%',
            }}>
                <View style={{
                    paddingHorizontal: 15,
                    paddingVertical: 15,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                }}>
                    <View onTouchEnd={() =>
                        navigation.pop(1)
                    }>
                        <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width='24' height='24'>
                            <Path strokeLinecap="round" stroke={'#555770'} strokeLinejoin="round" d="M6.75 15.75L3 12m0 0l3.75-3.75M3 12h18" />
                        </Svg>
                    </View>
                    <View>
                        <Text style={{
                            fontSize: 56,
                            fontWeight: '800',
                            color: '#555770'
                        }}>Scores</Text>
                    </View>
                </View>
                <View style={{
                    paddingHorizontal: 20,
                    width: '100%',
                    flexDirection: 'row',
                    paddingVertical: 5
                    // backgroundColor: 'green',
                }}>
                    <View style={{
                        width: '10%',
                        justifyContent: 'center',
                    }}>
                        <Text style={{
                            color: '#555770',
                            flexShrink: 0,
                            width: 30
                        }}>{' '}</Text>
                    </View>

                    <View style={{
                        width: '35%',
                        justifyContent: 'center',
                    }}>
                        <Text style={{
                            color: '#555770',
                            fontWeight: '800',
                            textTransform: 'uppercase',
                            fontSize: 18,
                        }}>{'difficulty'}</Text>

                    </View>
                    <View style={{
                        width: '30%',
                        justifyContent: 'center',
                    }}>
                        <Text style={{
                            color: '#555770',
                            fontWeight: '800',
                            textTransform: 'uppercase',
                            fontSize: 18,
                        }}>{'Date'}</Text>
                    </View>
                    <View style={{
                        width: '30%',
                        justifyContent: 'center',
                    }}>
                        <Text style={{
                            color: '#555770',
                            fontWeight: '800',
                            textTransform: 'uppercase',
                            fontSize: 18,
                        }}>{'duration'}</Text>


                    </View>
                    {/* <View style={{
                        width: '30%',
                        justifyContent: 'center',
                    }}>
                        <Text style={{
                            color: '#555770',
                            fontWeight: '800',
                            textTransform: 'uppercase',,
                            fontSize: 18,
                            textAlign: 'right',
                            flexShrink: 0,
                        }}>{'score'}</Text>
                    </View> */}

                </View>
                <ScrollView>
                    <View style={{
                        paddingHorizontal: 20,
                        width: '100%',
                        // backgroundColor: 'green'
                    }}>
                        {
                            scores.map((val, i) => (
                                <View key={i} style={{
                                    paddingHorizontal: 20,
                                    width: '100%',
                                    flexDirection: 'row',
                                    paddingVertical: 5
                                    // backgroundColor: 'green',
                                }}>
                                    <View style={{
                                        width: '5%',
                                        justifyContent: 'center',
                                    }}>
                                        <Text style={{
                                            color: '#555770',
                                            flexShrink: 0,
                                        }}>{i + 1}</Text>
                                    </View>

                                    <View style={{
                                        width: '35%',
                                        justifyContent: 'center',
                                    }}>
                                        <Text style={{
                                            color: '#555770',
                                            fontWeight: '800',
                                            textTransform: "capitalize"
                                        }}>{val.difficulty}</Text>

                                    </View>
                                    <View style={{
                                        width: '30%',
                                        justifyContent: 'center',
                                    }}>
                                        <Text style={{
                                            color: '#555770',
                                            fontWeight: '800',
                                            textTransform: 'uppercase'
                                        }}>{moment(val.time).format('ll')}</Text>
                                    </View>
                                    <View style={{
                                        width: '30%',
                                        justifyContent: 'center',
                                    }}>
                                        <Text style={{
                                            color: '#555770',
                                            fontWeight: '800',
                                            textTransform: 'lowercase'
                                        }}>{Math.floor(val.duration / 60000)} min</Text>


                                    </View>
                                    {/* <View style={{
                                        width: '30%',
                                        justifyContent: 'center',
                                    }}>
                                        <Text style={{
                                            color: '#555770',
                                            fontWeight: '800',
                                            textTransform: 'uppercase',
                                            textAlign: 'right',
                                            flexShrink: 0,
                                        }}>{'score'}</Text>
                                    </View> */}

                                </View>
                            ))
                        }

                    </View>
                </ScrollView>
            </View>
        </SafeAreaView>
    )
}

const mapStateToProps = state => ({
    scores: state.scores.list
})

export default connect(mapStateToProps, null)(ScoreBoardScreen)