import React from 'react'
import { View, Text } from 'react-native'
import <PERSON><PERSON><PERSON>ie<PERSON> from "lottie-react-native"
import loadingJSON from 'assets/loading.json'
import { StatusBar } from 'react-native'

const WelcomeLoading = () => {
    return (
        <>

            <StatusBar barStyle="light-content" backgroundColor={'transparent'} translucent={true} />
            <View style={{ flex: 1, backgroundColor: '#000' }}>
                <LottieView source={loadingJSON} autoPlay loop />
            </View>
        </>
    )
}

export default WelcomeLoading;

