// import { StatusBar } from 'expo-status-bar';
import { useEffect, useState, memo, useRef } from "react";
import {
  StyleSheet,
  Text,
  View,
  useWindowDimensions,
  TouchableHighlight,
  Image,
} from "react-native";
import Svg, { Path } from "react-native-svg";

import PlayPause from "src/components/Play-pause.component";
import GameView from "src/components/gameView/Game-view.component";
import GenerateGame from "src/lib/generator/sudoku";
import { connect } from "react-redux";
import { MAX_ALLOWD_MISTAK, MAX_HINT } from "src/constants/game.constants";
import { setNewScore } from "src/redux/scores/scores.action";
import Win from "./win.component";


const keyMap = [
  [1, 2, 3, 4, 5],
  [6, 7, 8, 9, 10],
];

const GameIndex = ({
  route,
  navigation,
  sudoku,
  isPlaying = true,
  timerHandler,
  // Actions
  setScore,
  isGameFinised,
  setIsGameFinised
}) => {
  const [puzzle, setPuzzle] = useState(sudoku.puzzle); // User will sovle
  const [solvedPuzzle, setSolvedPuzzle] = useState(sudoku.solvedPuzzle); // Generated solved puzzle
  const [unSolvedPuzzle, setUnSolvedPuzzle] = useState(sudoku.unSolvedPuzzle); // Generated unsolved puzzle
  const [game, setGame] = useState(sudoku); // Generated unsolved puzzle
  const [difficulty, setDifficulty] = useState(sudoku.difficulty); // Leval of game
  // const [puzzle, setPuzzle] = useState(GenerateGame.emptySudoku) // User will sovle
  // const [solvedPuzzle, setSolvedPuzzle] = useState(GenerateGame.emptySudoku) // Generated solved puzzle
  // const [unSolvedPuzzle, setUnSolvedPuzzle] = useState(GenerateGame.emptySudoku) // Generated unsolved puzzle
  // const [game, setGame] = useState(null) // Generated unsolved puzzle
  const [steps, setSteps] = useState([]); // keep track of inputs
  const [mistakeCount, setMistakeCount] = useState({ total: MAX_ALLOWD_MISTAK, used: 0 }); // Lifeline
  const [playPause, setPlayPause] = useState(true); // Game state
  const [totalHint, setTotalHint] = useState(MAX_HINT); // hint
  const [activeInput, setActiveInput] = useState({
    i: 0,
    j: 0,
    num: 0,
    isValidSelection: true,
  }); // Current selected grid item's index

  const currentTime = useRef(0)

  const { height, width, fontScale } = useWindowDimensions();
  const squareL1Width = (width - 20) / 3;
  const squareL2Width = (squareL1Width - 2) / 3;
  console.log(height, 'h');
  const activateInput = (i, j, num, e) => {
    // Do nothign if game is paused
    if (!isPlaying) return;
    console.log(game.isValidSelection(i, j));
    setActiveInput((state) => ({
      ...state,
      i,
      j,
      num,
      isValidSelection: game.isValidSelection(i, j),
    }));
  };

  const onKeyPress = (num) => {
    console.log({
      num,
      activeInput,
      unNum: unSolvedPuzzle[activeInput.i][activeInput.j],
      isValidIN: game.isValidInput(activeInput.i, activeInput.j, num),
    });
    // Do nothign if game is paused
    if (!isPlaying) return;
    const puzzleState = [...puzzle];
    if (unSolvedPuzzle[activeInput.i][activeInput.j] !== 0) {
      return;
    }
    puzzleState[activeInput.i][activeInput.j] = num;
    setPuzzle(puzzleState);
    const isValidInput = game.isValidInput(activeInput.i, activeInput.j, num);
    if (isValidInput) {
    } else {
      setMistakeCount((state) => ({
        ...state,
        used: state.used + 1 < state.total ? state.used + 1 : state.total,
      }));
    }
    setSteps((state) => [
      ...state,
      {
        i: activeInput.i,
        j: activeInput.j,
        num: num,
        isAdded: true,
      },
    ]);
  };

  const onClearPress = () => {
    // Do nothign if game is paused
    if (!isPlaying) return;
    const puzzleState = [...puzzle];
    if (
      unSolvedPuzzle[activeInput.i][activeInput.j] == 0 &&
      puzzleState[activeInput.i][activeInput.j] != 0
    ) {
      const currentValue = puzzleState[activeInput.i][activeInput.j];
      puzzleState[activeInput.i][activeInput.j] = 0;
      setPuzzle(puzzleState);
      setSteps((state) => [
        ...state,
        {
          i: activeInput.i,
          j: activeInput.j,
          num: currentValue,
          isAdded: false,
        },
      ]);
    }
  };

  const onHint = () => {
    // Do nothign if game is paused
    if (!isPlaying) return;
    const currectAns = solvedPuzzle[activeInput.i][activeInput.j];
    const currentAns = puzzle[activeInput.i][activeInput.j];
    if (currectAns != currentAns && totalHint > 0) {
      const state = [...puzzle];
      // Set puzzel with currect ANS
      state[activeInput.i][activeInput.j] = currectAns;
      setPuzzle(state);
      // Update unSolvedPuzzle to make hint input constant
      setUnSolvedPuzzle((unsp) => {
        unsp[activeInput.i][activeInput.j] = currectAns;
        return unsp;
      });
      // Remove steps if exist for this input
      setSteps((state) => {
        state = state.filter(
          (ele) => ele.i != activeInput.i && ele.j != activeInput.j
        );
        return state;
      });
      // Update hint count
      setTotalHint(h => (h - 1))
    }
  };

  const onUndo = () => {
    // Do nothign if game is paused
    if (!isPlaying) return;
    const puzzleState = [...puzzle];
    const step = steps.pop();
    if (!step) return;
    if (step.isAdded) {
      puzzleState[step.i][step.j] = 0;
    } else {
      puzzleState[step.i][step.j] = step.num;
    }
    setPuzzle(puzzleState);
  };

  useEffect(() => {
    // Check win on effect
    // Compare Solved and unsolved array
    if (game.isSolved()) {
      setScore({
        id: game.id,
        difficulty: difficulty,
        score: 123,
        duration: currentTime.current,
        hints: MAX_HINT - totalHint,
        mistaks: mistakeCount.used,
        time: Date.now()
      })
      if (setIsGameFinised) {
        setIsGameFinised(true)
      }

    }
  }, [puzzle]);

  return (
    <>
      <View
        style={{
          alignItems: "center",
          flexDirection: "column",
          // paddingTop: 5,
          // justifyContent: "center"
        }}
      >
        <View
          style={{
            justifyContent: "space-between",
            flexDirection: "row",
            width: width - 30,
            alignItems: "center",
          }}
        >
          <View>
            <Text
              style={{
                color: "#555770",
                fontSize: 24,
                fontWeight: "800",
                textTransform: "capitalize",
              }}
            >
              {difficulty.replace('-', ' ')}
            </Text>
          </View>
          <View>
            <Text
              style={{
                color: "#555770",
                fontSize: 16,
                fontWeight: "700",
                textTransform: "capitalize",
              }}
            >
              MISTAKE:{" "}
              <Text style={{ color: "#b95a5e" }}>
                {mistakeCount.used}/{mistakeCount.total}
              </Text>
            </Text>
          </View>
        </View>
        <View style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
        }} >
          <View
            style={{
              marginTop: 5,
              alignItems: "center"
            }} onLayout={(e) => {
              console.log(e.nativeEvent.layout);
            }}
          >
            {/* Sudoku Grid */}
            <GameView
              puzzle={isPlaying ? puzzle : GenerateGame.emptySudoku}
              activeInput={activeInput}
              solvedPuzzle={solvedPuzzle}
              unSolvedPuzzle={unSolvedPuzzle}
              activateInput={activateInput}
            />

            {/* Action buttons */}
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                alignItems: "center",
                width: width - 30,
                paddingVertical: 12,
              }}
            >
              <View style={{ flexDirection: "row" }}>
                {/* Undo button */}
                <TouchableHighlight
                  disabled={!isPlaying}
                  underlayColor="#DDDDDD"
                  style={styles.editButton}
                  onPress={(e) => {
                    onUndo();
                  }}
                >
                  <Svg
                    width="24px"
                    height="24px"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <Path
                      d="M3 9V12M9 15H3L5.64028 12.6307C7.02129 11.2521 8.81296 10.3596 10.7453 10.0878C12.6777 9.81593 14.6461 10.1794 16.3539 11.1234C18.0617 12.0675 19.4164 13.5409 20.2139 15.3218"
                      stroke="#001A72"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </Svg>
                </TouchableHighlight>
                {/* Hint Button */}
                <TouchableHighlight
                  disabled={!isPlaying}
                  underlayColor="#DDDDDD"
                  style={styles.editButton}
                  onPress={(e) => {
                    onHint();
                  }}
                >
                  <Svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    width="24px"
                    height="24px"
                  >
                    <Path
                      strokeLinecap="round"
                      stroke="#001A72"
                      strokeLinejoin="round"
                      d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"
                    />
                  </Svg>
                </TouchableHighlight>
                {/* Note Button */}
                <TouchableHighlight
                  disabled={!isPlaying}
                  underlayColor="#DDDDDD"
                  style={styles.editButton}
                >
                  <Svg
                    xmlns="http://www.w3.org/2000/S"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    width="24px"
                    height="24px"
                  >
                    <Path
                      strokeLinecap="round"
                      stroke="#001A72"
                      strokeLinejoin="round"
                      d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                    />
                  </Svg>
                </TouchableHighlight>
              </View>
              <PlayPause playPause={!isGameFinised} onRunning={(s) => { currentTime.current = s }} state={isPlaying} onClick={timerHandler} />
            </View>
            {/* num Keys */}
            <View
              style={{
                width: width - 30,
                justifyContent: "space-between",
                flexWrap: "wrap",
                flexDirection: "row",
              }}
            >
              {keyMap.map((val, i) => (
                <View key={i} style={styles.buttonContainer}>
                  {val.map((num, j) =>
                    num == 10 ? (
                      <TouchableHighlight
                        disabled={!isPlaying}
                        key={j}
                        underlayColor="#DDDDDD"
                        style={{ ...styles.numButton, backgroundColor: "#7e88ab" }}
                        onPress={(e) => {
                          onClearPress(0);
                        }}
                      >
                        <Svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          width="24px"
                          height="24px"
                        >
                          <Path
                            strokeLinecap="round"
                            stroke="#fff"
                            strokeLinejoin="round"
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </Svg>
                      </TouchableHighlight>
                    ) : (
                      <TouchableHighlight
                        disabled={!isPlaying}
                        underlayColor="#DDDDDD"
                        key={j}
                        style={styles.numButton}
                        onPress={(e) => {
                          onKeyPress(num);
                        }}
                      >
                        <Text style={styles.numButtonText}>{num}</Text>
                      </TouchableHighlight>
                    )
                  )}
                </View>
              ))}
            </View>
          </View>
        </View>
      </View >
      {
        isGameFinised &&
        <Win statics={{
          difficulty,
          time: currentTime.current
        }} navigation={navigation} />
      }
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginBottom: 16,
  },
  numButton: {
    width: 52,
    height: 52,
    marginRight: 10,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    borderColor: "#eceff7f5",
    borderWidth: 1,
    backgroundColor: "#eceff7f5",
    shadowColor: "#1f222a",
    shadowOffset: {
      width: 0,
      height: 18,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10.0,
    elevation: 24,
    borderColor: '#fff'
  },
  actionButton: {
    backgroundColor: "rgba(198, 65, 39, 0.8)",
    alignSelf: "stretch",
    flex: 1,
    width: "90%",
    textAlign: "center",
    height: 64,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 10,
    marginTop: 10,
    marginRight: 10,
  },
  numButtonText: {
    fontSize: 28,
    color: "#555770",
  },
  activeInput: {
    backgroundColor: "#a944425e",
  },
  editButton: {
    width: 52,
    height: 52,
    marginRight: 10,
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    borderColor: "#eceff7f5",
    borderWidth: 1,
    backgroundColor: "#eceff7f5",
    borderColor: '#fff',
    shadowColor: "#1f222a",
    shadowOffset: {
      width: 0,
      height: 18,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10.0,
    elevation: 24,
  },
  playPause: {
    width: 36,
    height: 36,
    marginLeft: 10,
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    borderColor: "#eceff7f5",
    borderWidth: 1,
    backgroundColor: "#eceff7f5",
    shadowColor: "#1f222a",
    shadowOffset: {
      width: 0,
      height: 18,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10.0,
    elevation: 24,
  },
});

const mapDispatchToProps = dispatch => ({
  setScore: (data) => dispatch(setNewScore(data))
})


export default connect(null, mapDispatchToProps)(GameIndex);
