import { useEffect, useState, memo, useMemo } from 'react';
import { StyleSheet, Text, View, TouchableHighlight } from 'react-native';
import Svg, { Path } from "react-native-svg"

const PlayPause = ({ onPause, onPlay, onClick, playPause = true, onRunning, state = true }) => {
    const [timeSec, setTimeSec] = useState(0);
    const [isPlay, setIsPlay] = useState(true)
    const [id, setId] = useState(null)
    const min = Math.floor(timeSec / 60);
    const second = timeSec - min * 60;

    const start = () => {
        const i = setInterval(() => {
            setTimeSec(sec => {
                if (onRunning) {
                    onRunning(sec + 1)
                }
                return (sec + 1)
            })
        }, 1000)
        setId(i)
        if (onPlay) {
            onPlay()
        }
    }
    const pause = () => {
        clearInterval(id)
        if (onPause) {
            onPause()
        }
    }
    useEffect(() => {
        if (!isPlay || !state || !playPause) { pause() }
        else { start() };
        return () => {
            pause()
        }
    }, [isPlay, state, playPause, playPause])

    return (
        <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#d6dae6',
            paddingVertical: 4,
            paddingLeft: 20,
            paddingRight: 10,
            borderRadius: 30
        }}>
            <Text style={{
                color: '#555770',
                fontSize: 20,
                fontWeight: '700',
                textTransform: 'capitalize'
            }}>{min < 10 ? "0" + min : min}:{second < 10 ? "0" + second : second}</Text>
            <TouchableHighlight underlayColor="#DDDDDD" style={styles.playPause} onPressOut={(e) => {
                const currentState = isPlay
                setIsPlay(!currentState)
                if (onClick) {
                    onClick(!currentState)
                }
            }}>
                {
                    isPlay ?
                        <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width='18px' height='18px'>
                            <Path stroke="#001A72" strokeLinecap="round" strokeLinejoin="round" d="M15.75 5.25v13.5m-7.5-13.5v13.5" />
                        </Svg>
                        :
                        <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width='18px' height='18px'>
                            <Path strokeLinecap="round" stroke="#001A72" strokeLinejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.348a1.125 1.125 0 010 1.971l-11.54 6.347a1.125 1.125 0 01-1.667-.985V5.653z" />
                        </Svg>

                }
            </TouchableHighlight>
        </View>
    )
}

export default PlayPause;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: 10
    },
    numButton: {
        width: 56,
        height: 56,
        marginRight: 10,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
        borderColor: '#eceff7f5',
        borderWidth: 1,
        backgroundColor: '#eceff7f5',
        shadowColor: "#1f222a",
        shadowOffset: {
            width: 0,
            height: 18,
        },
        shadowOpacity: 0.25,
        shadowRadius: 10.00,
        elevation: 24
    },
    actionButton: {
        backgroundColor: 'rgba(198, 65, 39, 0.8)',
        alignSelf: 'stretch',
        flex: 1,
        width: '90%',
        textAlign: 'center',
        height: 64,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 10,
        marginTop: 10,
        marginRight: 10
    },
    numButtonText: {
        fontSize: 28,
        color: '#555770'
    },
    activeInput: {
        backgroundColor: '#a944425e',
    },
    editButton: {
        width: 56,
        height: 56,
        marginRight: 10,
        borderRadius: 50,
        justifyContent: 'center',
        alignItems: 'center',
        borderColor: '#eceff7f5',
        borderWidth: 1,
        backgroundColor: '#eceff7f5',
        shadowColor: "#1f222a",
        shadowOffset: {
            width: 0,
            height: 18,
        },
        shadowOpacity: 0.25,
        shadowRadius: 10.00,
        elevation: 24
    },
    playPause: {
        width: 36,
        height: 36,
        marginLeft: 10,
        borderRadius: 50,
        justifyContent: 'center',
        alignItems: 'center',
        borderColor: '#eceff7f5',
        borderWidth: 1,
        backgroundColor: '#eceff7f5',
        shadowColor: "#1f222a",
        shadowOffset: {
            width: 0,
            height: 18,
        },
        shadowOpacity: 0.25,
        shadowRadius: 10.00,
        elevation: 24
    }
});
