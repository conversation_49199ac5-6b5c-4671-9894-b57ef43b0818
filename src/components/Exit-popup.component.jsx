const OptionButton = ({ children, ...props }) => {
    const styles = StyleSheet.create({
        optionButton: {
            width: '100%'
        }
    })
    return (
        <TouchableHighlight {...props} underlayColor="#DDDDDD" style={styles.optionButton} >
            <LinearGradient start={[0, 0.5]}
                end={[1, 0.5]}
                colors={['transparent', '#55577021', '#555770', '#55577021', 'transparent']}
                style={{ paddingTop: 0.6, alignItems: 'center' }}>
                <LinearGradient start={[0, 0.5]}
                    end={[1, 0.5]}
                    colors={['#transparent', '#fff', '#fff']}
                    style={{
                        width: '100%', alignItems: 'center'
                    }}>
                    <View style={{
                        width: '100%',
                        paddingVertical: 15,
                        alignItems: 'center',
                    }}>
                        <Text>{children}</Text>
                    </View>
                </LinearGradient>
            </LinearGradient>
        </TouchableHighlight>)
}

const ExitPopup = ({ onCancel, onExit }) => {
    const styles = StyleSheet.create({
        popupContainer: {
            position: 'absolute',
            bottom: 0,
            top: 0,
            right: 0,
            left: 0,
            width: '100%',
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            backgroundColor: '#eceff7c2',
            paddingHorizontal: 15,
            paddingBottom: 5,
            justifyContent: 'flex-end'
        },
        buttonContainer: {
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center'
        },
        optionButton: {
            paddingVertical: 8,
            width: '100%'
        }
    })
    return (
        <View style={styles.popupContainer}>
            <View style={styles.buttonContainer}>
                <OptionButton >New Game</OptionButton>
                <OptionButton >Save</OptionButton>
                <OptionButton onPress={onExit} >Exit</OptionButton>
                <OptionButton onPress={onCancel} >Cancel</OptionButton>
            </View>
        </View>
    )
}