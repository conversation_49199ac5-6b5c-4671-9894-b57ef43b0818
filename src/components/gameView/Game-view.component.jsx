// import { StatusBar } from 'expo-status-bar';
import { useEffect, useState, memo, useMemo } from 'react';
import { StyleSheet, Text, Platform, View, SafeAreaView, StatusBar, useWindowDimensions, TouchableHighlight } from 'react-native';



const GameView = ({ puzzle, activateInput, activeInput, solvedPuzzle = [], unSolvedPuzzle = [] }) => {
    const { height, width, fontScale } = useWindowDimensions();
    const squareL1Width = (width - 20) / 3
    const squareL2Width = (squareL1Width - 1) / 3

    return (
        < View style={{
            justifyContent: 'center',
            alignItems: 'center',
            height: width - 20,
            width: width,
            flexWrap: 'wrap',
            flexDirection: 'row',
            // flexDirection: 'column',
        }
        }>
            {
                puzzle.map((grid, i) => (
                    <View key={i} style={{
                        height: squareL1Width,
                        width: squareL1Width,
                        borderWidth: 0.5,
                        borderColor: '#c8cde4',
                        flexWrap: 'wrap',
                        borderRadius: 7,
                        overflow: 'hidden',
                        // flexDirection: 'column',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'stretch',
                        shadowColor: "#555770",
                        shadowOffset: {
                            width: 0,
                            height: 1,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 2.22,
                        elevation: 3,
                        backgroundColor: '#fff',
                    }}>
                        {
                            grid.map((val, j) => (
                                <View key={j} style={{
                                    height: squareL2Width,
                                    width: squareL2Width,
                                    backgroundColor: '#fff',
                                    borderWidth: 0.5,
                                    borderColor: '#c8cde4',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    ...(activeInput.i == i && activeInput.j == j) ?
                                        activeInput.isValidSelection ?
                                            styles.validSelection : styles.inValidSelection
                                        : {},
                                    ...(unSolvedPuzzle[i][j] == 0 && val != 0) ?
                                        (val == solvedPuzzle[i][j]) ?
                                            {} : styles.inValidSelection
                                        : {},
                                    ...(val == activeInput.num && (activeInput.i != i && activateInput.j != j) && val != 0) ? styles.matchedValues : {}
                                }} onTouchEnd={e => activateInput(i, j, val, e)}  >
                                    <Text style={{
                                        fontSize: 28 / fontScale,
                                        color: '#555770'
                                    }}>{val ? val : ""}</Text>
                                </View>
                            ))
                        }
                    </View>
                ))
            }
        </View >

    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around'
    },
    button: {
        backgroundColor: 'rgba(198, 65, 39, 0.8)',
        width: 64,
        height: 64,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 50,
        marginRight: 10,
        marginTop: 10
    },
    actionButton: {
        backgroundColor: 'rgba(198, 65, 39, 0.8)',
        alignSelf: 'stretch',
        flex: 1,
        width: '90%',
        textAlign: 'center',
        height: 64,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 10,
        marginTop: 10,
        marginRight: 10
    },
    numButtonText: {
        fontSize: 28,
        color: '#fff'
    },
    validSelection: {
        backgroundColor: '#bdc5de85',
    },
    inValidSelection: {
        backgroundColor: '#d69375b3',
    },
    activeInput: {
        backgroundColor: '#bdc5decc',
    },
    matchedValues: {
        backgroundColor: '#bdc5de70'
    },
    selectedBlock: {
        backgroundColor: '#bdc5de85'
    }
});

export default memo(GameView)
