import { Platform, View, SafeAreaView as SafeArea, StatusBar, useWindowDimensions } from 'react-native';

const SafeAreaView = ({ children }) => {
    const { height, width, fontScale } = useWindowDimensions();
    return (
        <SafeArea >
            <View style={{
                paddingTop: Platform.OS == "android" ? StatusBar.currentHeight : 0,
                alignItems: 'center',
                flexDirection: 'column',
                backgroundColor: '#eceff7c2',
                minHeight: height + (Platform.OS == "android" ? StatusBar.currentHeight : 0)
            }}>
                {children}
            </View>
        </SafeArea >
    )
}

export default SafeAreaView;