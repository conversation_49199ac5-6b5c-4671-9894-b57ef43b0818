import React from 'react'
import { StatusBar, Text } from 'react-native';
import { Image, View } from 'react-native';
import { TouchableHighlight } from 'react-native';
import { StackActions } from '@react-navigation/native';
import { connect } from 'react-redux';
import { cleareGame } from 'src/redux/game/game.action';

const popAction = StackActions.pop(1);

const Win = ({ navigation, statics, gameClear }) => {

    const onNextPress = () => {
        if (!navigation) return;
        gameClear();
        navigation.dispatch(popAction);
    }
    const min = Math.floor(statics.time / 60);
    const second = statics.time - min * 60;
    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor={'transparent'} translucent={true} />
            <View style={{
                position: "absolute",
                top: 0,
                bottom: 0,
                right: 0,
                left: 0,
                // backgroundColor: '#eceff7ec',
                backgroundColor: '#7e88ab',
                justifyContent: "center",
                alignItems: "center"
            }}>
                <Text style={{
                    fontSize: 14,
                    fontWeight: '300',
                    fontFamily: 'Lato_300Light',
                    textTransform: 'uppercase',
                    color: '#fff'
                }}>{statics.difficulty}</Text>
                <Text
                    style={{
                        color: "#555770",
                        fontSize: 48,
                        fontWeight: "800",
                        textTransform: "capitalize",
                        color: '#fff'
                    }}
                >{min < 10 ? "0" + min : min}:{second < 10 ? "0" + second : second}</Text>
                <Image source={require('assets/win.gif')} resizeMode="contain" style={{
                    width: 300,
                    height: '100%',
                    maxHeight: 300,
                    marginBottom: 24
                }} />
                <Text
                    style={{
                        color: "#555770",
                        fontSize: 32,
                        fontWeight: "800",
                        textTransform: "capitalize",
                        color: '#fff'
                    }}
                >
                    {'Congratulations'}
                </Text>
                <Text style={{
                    fontSize: 16,
                    fontWeight: '200',
                    fontFamily: 'Lato_300Light',
                    marginTop: 8,
                    color: '#fff'
                }}>You are a champ now</Text>
                <TouchableHighlight
                    underlayColor="#DDDDDD"
                    style={{
                        paddingHorizontal: 40,
                        marginTop: 40,
                        height: 52,
                        marginRight: 10,
                        borderRadius: 10,
                        justifyContent: "center",
                        alignItems: "center",
                        borderColor: "#eceff7f5",
                        borderWidth: 1,
                        backgroundColor: "#eceff7f5",
                        shadowColor: "#1f222a",
                        shadowOffset: {
                            width: 0,
                            height: 18,
                        },
                        shadowOpacity: 0.25,
                        shadowRadius: 10.0,
                        elevation: 24,
                    }}
                    onPress={onNextPress}
                >
                    <Text style={{
                        fontSize: 18,
                        fontWeight: '800',
                        color: "#555770",
                    }}>{'New Game'}</Text>
                </TouchableHighlight>
            </View>
        </>
    )
}

const mapDispatchToProps = dispatch => ({
    gameClear: (data) => dispatch(cleareGame(data))
})

export default connect(null, mapDispatchToProps)(Win);