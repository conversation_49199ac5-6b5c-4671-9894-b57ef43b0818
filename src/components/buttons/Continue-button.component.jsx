import React from 'react'
import { View, Text, TouchableHighlight, useWindowDimensions, StyleSheet } from 'react-native'
import { Path, Svg } from 'react-native-svg';
import {
    useFonts,
    Lato_100Thin,
    Lato_300Light,
    Lato_400Regular
} from "@expo-google-fonts/lato"

const ContinueButtons = ({ children, title, onPress }) => {
    const { height, width, fontScale } = useWindowDimensions();
    let [isFontsLoaded] = useFonts({ Lato_100Thin, Lato_300Light, Lato_400Regular });
    if (!isFontsLoaded) {
        return <></>
    }
    return (
        <TouchableHighlight underlayColor="transparent" onPress={(e) => {
            if (onPress) {
                onPress(e)
            }
        }} style={{
            minWidth: 254,
            width: '70%',
            alignItems: 'flex-start',
            justifyContent: 'center',
            marginTop: 8
        }}>
            <View style={{
                flexDirection: 'row',
            }}>
                <View style={{
                    ...style.button,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width='24px' height='24px'>
                        <Path strokeLinecap="round" strokeLinejoin="round" stroke="#001A72" d="M21 7.5V18M15 7.5V18M3 16.811V8.69c0-.864.933-1.406 1.683-.977l7.108 4.061a1.125 1.125 0 010 1.954l-7.108 4.061A1.125 1.125 0 013 16.811z" />
                    </Svg>
                </View>
                <View style={{
                    ...style.button,
                    height: 54,
                    justifyContent: 'center',
                    width: '100%',
                    marginLeft: 5,
                    paddingLeft: 30,
                    borderRadius: 10,
                }}>
                    <Text style={{
                        fontSize: 20,
                        fontWeight: '900',
                        fontFamily: 'Lato_100Thin'
                    }}>
                        {title || children}
                    </Text>

                </View>
            </View>
        </TouchableHighlight>
    )
}

const style = StyleSheet.create({
    button: {
        width: 56,
        height: 56,
        marginRight: 10,
        borderRadius: 10,
        borderColor: '#eceff7f5',
        borderWidth: 1,
        backgroundColor: '#eceff7f5',
        shadowColor: "#1f222a",
        shadowOffset: {
            width: 0,
            height: 18,
        },
        shadowOpacity: 0.25,
        shadowRadius: 10.00,
        elevation: 24,
        color: '#555770'
    },
})

export default ContinueButtons;