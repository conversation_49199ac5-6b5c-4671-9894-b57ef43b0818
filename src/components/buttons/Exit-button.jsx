import React from 'react'
import { View, Text, TouchableHighlight, useWindowDimensions, StyleSheet } from 'react-native'
import { Path, Svg } from 'react-native-svg';

const ExitButton = ({ children, title, onPress }) => {
    const { height, width, fontScale } = useWindowDimensions();
    return (
        <TouchableHighlight underlayColor="transparent" onPress={(e) => {
            if (onPress) {
                onPress(e)
            }
        }} style={{
            minWidth: 254,
            width: '70%',
            alignItems: 'flex-start',
            justifyContent: 'center',
            marginTop: 10
        }}>
            <View style={{
                flexDirection: 'row',
            }}>
                <View style={{
                    ...style.button,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width={'24px'} height={'24px'}>
                        <Path strokeLinecap="round" stroke={'#001A72'} strokeLinejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                    </Svg>

                </View>
                <View style={{
                    ...style.button,
                    height: 54,
                    justifyContent: 'center',
                    width: '100%',
                    marginLeft: 5,
                    paddingLeft: 30,
                    borderRadius: 10,
                }}>
                    <Text style={{
                        fontSize: 20,
                        fontWeight: '900',
                    }}>
                        {title || children}
                    </Text>

                </View>
            </View>
        </TouchableHighlight>
    )
}

const style = StyleSheet.create({
    button: {
        width: 56,
        height: 56,
        marginRight: 10,
        borderRadius: 10,
        borderColor: '#eceff7f5',
        borderWidth: 1,
        backgroundColor: '#eceff7f5',
        shadowColor: "#1f222a",
        shadowOffset: {
            width: 0,
            height: 18,
        },
        shadowOpacity: 0.25,
        shadowRadius: 10.00,
        elevation: 24,
        color: '#555770'
    },
})

export default ExitButton