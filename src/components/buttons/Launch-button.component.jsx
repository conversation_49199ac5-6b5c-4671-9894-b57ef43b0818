import React from 'react'
import { View, Text, TouchableHighlight, useWindowDimensions, StyleSheet } from 'react-native'
import { Path, Svg } from 'react-native-svg';
import {
    useFonts,
    Lato_100Thin,
    Lato_300Light,
    Lato_400Regular
} from "@expo-google-fonts/lato"

const LaunchButton = ({ children, title, onPress }) => {
    const { height, width, fontScale } = useWindowDimensions();
    let [isFontsLoaded] = useFonts({ Lato_100Thin, Lato_300Light, Lato_400Regular });
    if (!isFontsLoaded) {
        return <></>
    }
    return (
        <TouchableHighlight underlayColor="transparent" onPress={(e) => {
            if (onPress) {
                onPress(e)
            }
        }} style={{
            minWidth: 254,
            width: '70%',
            alignItems: 'flex-start',
            justifyContent: 'center',
            marginTop: 8
        }}>
            <View style={{
                flexDirection: 'row',
            }}>
                <View style={{
                    ...style.button,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>

                </View>
                <View style={{
                    ...style.button,
                    height: 54,
                    justifyContent: 'center',
                    width: '100%',
                    marginLeft: 5,
                    paddingLeft: 30,
                    borderRadius: 10,
                }}>
                    <Text style={{
                        fontSize: 20,
                        fontWeight: '900',
                        fontFamily: 'Lato_300Light'
                    }}>
                        {title || children}
                    </Text>

                </View>
            </View>
        </TouchableHighlight>
    )
}

const style = StyleSheet.create({
    button: {
        width: 56,
        height: 56,
        marginRight: 10,
        borderRadius: 10,
        borderColor: '#eceff7f5',
        borderWidth: 1,
        backgroundColor: '#eceff7f5',
        shadowColor: "#1f222a",
        shadowOffset: {
            width: 0,
            height: 18,
        },
        shadowOpacity: 0.25,
        shadowRadius: 10.00,
        elevation: 24,
        color: '#555770'
    },
})

export default LaunchButton