import React from 'react'
import { View, Text, TouchableHighlight, useWindowDimensions, StyleSheet } from 'react-native'
import { Path, Svg } from 'react-native-svg';
import {
    useFonts,
    Lato_100Thin,
    Lato_300Light,
    Lato_400Regular
} from "@expo-google-fonts/lato"

const StartButton = ({ children, title, onPress, logo }) => {
    const { height, width, fontScale } = useWindowDimensions();
    let [isFontsLoaded] = useFonts({ Lato_100Thin, Lato_300Light, Lato_400Regular });
    if (!isFontsLoaded) {
        return <></>
    }
    return (
        <TouchableHighlight underlayColor="transparent" onPress={(e) => {
            if (onPress) {
                onPress(e)
            }
        }} style={{
            width: '46%',
            alignItems: 'flex-start',
            justifyContent: 'center',
            marginTop: 18,
            // marginHorizontal: 8,
            marginHorizontal: '2%'
        }}>
            <View style={{
                ...style.button,
                paddingVertical: 24

            }}>
                <View style={{
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    {children}
                </View>
                <View style={{
                    // ...style.button,
                    // height: 54,
                    justifyContent: 'center',
                    alignItems: 'center',
                    // // width: '100%',
                    // marginLeft: 5,
                    // paddingLeft: 30,
                    // borderRadius: 10,
                }}>
                    <Text style={{
                        marginTop: 4,
                        fontSize: 20,
                        fontWeight: '900',
                        fontFamily: 'Lato_100Thin',
                        color: '#555770'
                    }}>
                        {title}
                    </Text>
                </View>
            </View>
        </TouchableHighlight>
    )
}

const style = StyleSheet.create({
    button: {
        width: '100%',
        // height: 56,
        borderRadius: 10,
        // borderColor: '#eceff7f5',
        borderColor: '#fff',
        borderWidth: 1,
        backgroundColor: '#eceff7f5',
        shadowColor: "#1f222a",
        shadowOffset: {
            width: 0,
            height: 18,
        },
        shadowOpacity: 0.20,
        shadowRadius: 10.00,
        elevation: 12,
        color: '#555770'
    },
})

export default StartButton;