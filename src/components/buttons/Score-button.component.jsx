import React from 'react'
import { View, Text, TouchableHighlight, useWindowDimensions, StyleSheet } from 'react-native'
import { Path, Svg } from 'react-native-svg';
import {
    useFonts,
    Lato_100Thin,
    Lato_300Light,
    Lato_400Regular
} from "@expo-google-fonts/lato"
const ScoreButton = ({ children, title, onPress }) => {
    const { height, width, fontScale } = useWindowDimensions();
    let [isFontsLoaded] = useFonts({ Lato_100Thin, Lato_300Light, Lato_400Regular });
    if (!isFontsLoaded) {
        return <></>
    }
    return (
        <TouchableHighlight underlayColor="transparent" onPress={(e) => {
            if (onPress) {
                onPress(e)
            }
        }} style={{
            minWidth: 254,
            width: '70%',
            alignItems: 'flex-start',
            justifyContent: 'center',
            marginTop: 8
        }}>
            <View style={{
                flexDirection: 'row',
            }}>
                <View style={{
                    ...style.button,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" height='24px' width='24px' >
                        <Path strokeLinecap="round" stroke="#001A72" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                    </Svg>

                </View>
                <View style={{
                    ...style.button,
                    height: 54,
                    justifyContent: 'center',
                    width: '100%',
                    marginLeft: 5,
                    paddingLeft: 30,
                    borderRadius: 10,
                }}>
                    <Text style={{
                        fontSize: 20,
                        fontWeight: '900',
                        fontFamily: 'Lato_300Light'
                    }}>
                        {title || children}
                    </Text>

                </View>
            </View>
        </TouchableHighlight>
    )
}

const style = StyleSheet.create({
    button: {
        width: 56,
        height: 56,
        marginRight: 10,
        borderRadius: 10,
        borderColor: '#eceff7f5',
        borderWidth: 1,
        backgroundColor: '#eceff7f5',
        shadowColor: "#1f222a",
        shadowOffset: {
            width: 0,
            height: 18,
        },
        shadowOpacity: 0.25,
        shadowRadius: 10.00,
        elevation: 24,
        color: '#555770'
    },
})

export default ScoreButton