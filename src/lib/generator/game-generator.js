function solvedPuzzl() {
    // Initialize the grid
    let grid = [[0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ];

    // Fill in the diagonal blocks
    for (let i = 0; i < 9; i += 3) {
        for (let j = 0; j < 9; j += 3) {
            fillBlock(i, j);
        }
    }

    // Fill in the rest of the grid
    for (let i = 0; i < 9; i++) {
        for (let j = 0; j < 9; j++) {
            if (grid[i][j] === 0) {
                fillCell(i, j);
            }
        }
    }

    return grid;

    function fillBlock(row, col) {
        let nums = [1, 2, 3, 4, 5, 6, 7, 8, 9];
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                let r = row + i;
                let c = col + j;
                let randIndex = Math.floor(Math.random() * nums.length);
                grid[r][c] = nums.splice(randIndex, 1)[0];
            }
        }
    }

    function fillCell(row, col) {
        let nums = [1, 2, 3, 4, 5, 6, 7, 8, 9];
        for (let num of nums) {
            if (isSafe(row, col, num)) {
                grid[row][col] = num;
                return;
            }
        }

        for (let i = 0; i < 9; i++) {
            for (let j = 0; j < 9; j++) {
                if (grid[i][j] === 0) {
                    fillCell(i, j);
                    return;
                }
            }
        }
    }

    function isSafe(row, col, num) {
        return !usedInRow(row, num) && !usedInCol(col, num) && !usedInBlock(row, col, num);
    }

    function usedInRow(row, num) {
        for (let i = 0; i < 9; i++) {
            if (grid[row][i] === num) {
                return true;
            }
        }
        return false;
    }
}

const generateEmptySudoku = () => [
    [
        0, 0, 0, 0, 0,
        0, 0, 0, 0
    ],
    [
        0, 0, 0, 0, 0,
        0, 0, 0, 0
    ],
    [
        0, 0, 0, 0, 0,
        0, 0, 0, 0
    ],
    [
        0, 0, 0, 0, 0,
        0, 0, 0, 0
    ],
    [
        0, 0, 0, 0, 0,
        0, 0, 0, 0
    ],
    [
        0, 0, 0, 0, 0,
        0, 0, 0, 0
    ],
    [
        0, 0, 0, 0, 0,
        0, 0, 0, 0
    ],
    [
        0, 0, 0, 0, 0,
        0, 0, 0, 0
    ],
    [
        0, 0, 0, 0, 0,
        0, 0, 0, 0
    ]
]

const puzzleGame = (difficulty) => {
    const solvedGame = solvedPuzzl()
    const unSolved = JSON.parse(JSON.stringify(solvedGame));
    let nums = [1, 2, 3, 4, 5, 6, 7, 8, 9];
    // const rand = Math.random() * (min - max) + min;
    const rand = Math.random() * (50 - 65) + 50;
    for (let i = 0; i < rand; i++) {
        let r = Math.floor(Math.random() * nums.length);
        let c = Math.floor(Math.random() * nums.length);
        unSolved[r][c] = 0;
    }
    return {
        difficulty: 'easy',
        puzzle: unSolved,
        solvedPuzzl: solvedGame
    }

}

// export default puzzleGame

class GenerateGame {

    difficulty = 'easy'
    puzzle = []
    solvedPuzzle = []
    unSolvedPuzzle = []
    static emptySudoku = generateEmptySudoku()

    constructor(difficulty = 'easy') {
        this.difficulty = difficulty;
        const game = puzzleGame(difficulty)
        this.puzzle = JSON.parse(JSON.stringify(game.puzzle))
        this.unSolvedPuzzle = JSON.parse(JSON.stringify(game.puzzle))
        this.solvedPuzzle = JSON.parse(JSON.stringify(game.solvedPuzzl));
    }

    start = () => {
        return {
            difficulty: this.difficulty,
            puzzle: this.puzzle,
            solvedPuzzle: this.solvedPuzzle,
            unSolvedPuzzle: this.unSolvedPuzzle
        }
    }
    isValidInput = (i, j, num = 0) => {
        if (i < 0 || j < 0 || !num) return false
        return this.solvedPuzzle[i][j] == num
    }
    isValidSelection = (i, j) => {
        if (i < 0 || j < 0) return false
        return this.unSolvedPuzzle[i][j] !== 0 ? false : true
    }
}

export default GenerateGame;