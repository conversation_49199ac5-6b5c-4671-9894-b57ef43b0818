import getUUID from "../id-generator";

class Sudoku {
    // sudoku = root.sudoku = {};  // Global reference to the sudoku library
    static emptySudoku = [[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]]

    // Define difficulties by how many squares are given to the player in a new
    // puzzle.
    static DIFFICULTY_LAVAL = {
        // "easy": 78,
        "easy": 62,
        "medium": 53,
        "hard": 44,
        "very-hard": 35,
        "insane": 26,
        "inhuman": 17,
    };
    difficulty = 'easy'
    // Blank character and board representation

    puzzle = []
    solvedPuzzle = []
    unSolvedPuzzle = []
    id = 0
    constructor(difficulty = 'easy') {
        this.difficulty = difficulty
        const solvedPuzzle = this.#getSudoku(); // Create new solved sudoku game 9x9
        const puzzle = this.#getPuzzle(JSON.parse(JSON.stringify(solvedPuzzle)), difficulty); // Make a puzzle
        const unSolvedPuzzle = JSON.parse(JSON.stringify(puzzle)); // Copy puzzle
        this.id = getUUID()
        this.solvedPuzzle = solvedPuzzle;
        this.puzzle = puzzle;
        this.unSolvedPuzzle = unSolvedPuzzle;
    }

    isSolved() {
        return JSON.stringify(this.puzzle) == JSON.stringify(this.solvedPuzzle)
    }

    getGame = () => {
        return {
            difficulty: this.difficulty,
            puzzle: this.puzzle,
            solvedPuzzle: this.solvedPuzzle,
            unSolvedPuzzle: this.unSolvedPuzzle
        }
    }

    isValidInput = (i, j, num = 0) => {
        if (i < 0 || j < 0 || !num) return false
        return this.solvedPuzzle[i][j] == num
    }
    isValidSelection = (i, j) => {
        if (i < 0 || j < 0) return false
        return this.unSolvedPuzzle[i][j] !== 0 ? false : true
    }
    // Pass whatever the root object is, lsike 'window' in browsers

    #getSudoku = () => {
        let board = [];
        for (let i = 0; i < 9; i++) {
            board[i] = [];
            for (let j = 0; j < 9; j++) {
                board[i][j] = 0;
            }
        }

        const canPlace = (board, row, col, k) => {
            for (let i = 0; i < 9; i++) {
                if (board[row][i] === k || board[i][col] === k) {
                    return false;
                }
            }

            let rowStart = Math.floor(row / 3) * 3;
            let colStart = Math.floor(col / 3) * 3;

            for (let i = 0; i < 3; i++) {
                for (let j = 0; j < 3; j++) {
                    if (board[rowStart + i][colStart + j] === k) {
                        return false;
                    }
                }
            }

            return true;
        }

        const fillSudoku = (board, row, col) => {
            if (row === 9) {
                return true;
            }

            if (board[row][col] !== 0) {
                if (col === 8) {
                    if (fillSudoku(board, row + 1, 0)) {
                        return true;
                    }
                } else {
                    if (fillSudoku(board, row, col + 1)) {
                        return true;
                    }
                }

                return false;
            }
            let fillRandUni = (arr = [], numArr = [5, 7, 1, 8, 4, 9, 3, 2, 6]) => {
                if (numArr.length == 0) {
                    return arr
                }
                let ranI = Math.floor(Math.random() * ((numArr.length) - 0) + 0)
                let randInt = numArr[ranI]
                numArr.splice(ranI, 1)
                arr.push(randInt)
                return fillRandUni(arr, numArr)
            }
            for (let k of fillRandUni()) {
                // for (let k of fillRandUni()) {
                if (canPlace(board, row, col, k)) {
                    board[row][col] = k;

                    if (col === 8) {
                        if (fillSudoku(board, row + 1, 0)) {
                            return true;
                        }
                    } else {
                        if (fillSudoku(board, row, col + 1)) {
                            return true;
                        }
                    }

                    board[row][col] = 0;
                }
            }

            return false;
        }

        fillSudoku(board, 0, 0);
        return this.#makeGridPuzzle(board)
    }

    #makeGridPuzzle = (sudoku) => {
        let puzzle = []
        for (let s = 0; s < 9; s += 3) {
            let [a, b, c] = [[], [], []]
            for (let i = s; i < s + 3; i++) {
                let arr = sudoku[i]
                a = [...a, ...arr.splice(0, 3)]
                b = [...b, ...arr.splice(0, 3)]
                c = [...c, ...arr.splice(0, 3)]
            }
            puzzle = [...puzzle, a, b, c]
        }
        return puzzle;
    }

    #getPuzzle = (sudoku, difficulty = 'easy') => {
        const numOfEleToRemove = 80 - Sudoku.DIFFICULTY_LAVAL[difficulty]
        console.log(numOfEleToRemove, difficulty);
        let fillRandUni = (numArr = Array.apply(null, Array(81)).map((_, i) => i), count = 0) => {
            if (numArr.length == 0 || count >= numOfEleToRemove) {
                return sudoku
            }
            let ranI = Math.floor(Math.random() * ((numArr.length) - 0) + 0)
            let randInt = numArr[ranI]
            numArr.splice(ranI, 1)
            let i = Math.floor(randInt / 9)
            let j = randInt % 9
            sudoku[i][j] = 0
            return fillRandUni(numArr, ++count)
        }
        return fillRandUni()
    }

};

export default Sudoku;