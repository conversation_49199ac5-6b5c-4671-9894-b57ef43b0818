{"name": "sudoku-1", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-community/masked-view": "^0.1.11", "@react-navigation/drawer": "^6.6.0", "@react-navigation/native": "^6.1.3", "expo": "^47.0.13", "expo-ads-admob": "^13.0.0", "expo-linear-gradient": "~12.0.1", "expo-status-bar": "~1.4.2", "react": "18.1.0", "react-native": "0.70.5", "react-native-gesture-handler": "~2.8.0", "react-native-google-mobile-ads": "^9.1.1", "react-native-linear-gradient": "^2.6.2", "react-native-reanimated": "~2.12.0", "react-native-safe-area-context": "^4.4.1", "react-native-safe-area-view": "^1.1.1", "react-native-screens": "^3.20.0", "react-native-svg": "13.4.0"}, "devDependencies": {"@babel/core": "^7.12.9", "babel-plugin-module-resolver": "^5.0.0"}, "private": true}